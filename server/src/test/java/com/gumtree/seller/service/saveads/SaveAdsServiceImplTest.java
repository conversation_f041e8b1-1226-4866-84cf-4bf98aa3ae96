package com.gumtree.seller.service.saveads;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.gumtree.common.properties.GtPropManager;
import com.gumtree.common.properties.GtPropertiesInitializer;
import com.gumtree.common.properties.GtProps;
import com.gumtree.personalization.domain.savedsearches.entity.SavedAds;
import com.gumtree.personalization.repository.SavedAdsRepository;
import com.gumtree.seller.domain.user.exception.UserNotFoundException;
import com.gumtree.seller.repository.user.UserRepository;
import org.apache.commons.configuration.ConfigurationException;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;

import static com.gumtree.config.property.BapiServerProperty.FAVOURITE_ADVERTS_KAFKA_MIGRATION_ENABLED;
import static org.fest.assertions.api.Assertions.assertThat;
import static org.fest.assertions.api.Assertions.fail;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SaveAdsServiceImplTest {
    private static final long USER_ID = 1L;
    private static final long ADVERT_ID_1 = 99L;
    private static final long ADVERT_ID_2 = 100L;
    private static final long ADVERT_ID_3 = 101L;
    private static final long ADVERT_ID_4 = 102L;

    @InjectMocks private SaveAdsServiceImpl service;
    @Mock private SavedAdsRepository repository;
    @Mock private UserRepository userRepository;
    @Mock private SavedAdsMigrationService migrationService;

    @BeforeClass
    public static void beforeAll() {
        GtPropManager.setProperty(FAVOURITE_ADVERTS_KAFKA_MIGRATION_ENABLED.getPropertyName(), "true");
    }

    @Before
    public void before() {
        service.setLimit(3);
    }

    @Test
    public void shouldAddAdvertToUserSavedAdsSetIfUserHasNotSavedAdsYetAndUserExists() {
        // given
        when(repository.findByUserId(USER_ID)).thenReturn(null);
        when(userRepository.exists(USER_ID)).thenReturn(true);

        // when
        service.addAdvert(ADVERT_ID_1, USER_ID);

        // then
        verify(repository).findByUserId(USER_ID);
        verify(repository).save(SavedAds.builder().userId(USER_ID).advert(ADVERT_ID_1).build());
        verify(migrationService).notifyAdvertAdded(USER_ID, ADVERT_ID_1);
    }

    @Test
    public void shouldNotAddAdvertToUserSavedAdsSetIfUserHasNotSavedAdsYetAndUserDoNotExist() {
        // given
        when(repository.findByUserId(USER_ID)).thenReturn(null);
        when(userRepository.exists(USER_ID)).thenReturn(false);

        // when
        try {
            service.addAdvert(ADVERT_ID_1, USER_ID);
            fail("should not be reached");
        } catch (UserNotFoundException exc) {
            verifyNoInteractions(migrationService);
        }
    }

    @Test
    public void shouldAddAdvertToUserSavedAdsSetIfUserAlreadyHasSavedAds() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID).advert(ADVERT_ID_1).build());

        // when
        service.addAdvert(ADVERT_ID_2, USER_ID);

        // then
        verify(repository).findByUserId(USER_ID);
        verify(repository).save(SavedAds.builder().id("88").userId(USER_ID)
                .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2))).build());
        verify(migrationService).notifyAdvertAdded(USER_ID, ADVERT_ID_2);
    }

    @Test
    public void shouldRemoveOldestAndAddNewAdvertIfLimitIsReached() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID)
                        .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2, ADVERT_ID_3))).build());

        // when
        service.addAdvert(ADVERT_ID_4, USER_ID);

        // then
        verify(repository).findByUserId(USER_ID);
        verify(repository).save(SavedAds.builder().id("88").userId(USER_ID)
                .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_2, ADVERT_ID_3, ADVERT_ID_4))).build());
    }

    @Test
    public void shouldGetUserSavedAdvertsIfFound() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID)
                        .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2))).build());

        // when
        Set<Long> adverts = service.getAdverts(USER_ID);

        // then
        assertThat(adverts).isEqualTo(Sets.newHashSet(ADVERT_ID_1, ADVERT_ID_1 + 1));
    }

    @Test
    public void shouldReturnEmptySetIfUserHasNoSavedAds() {
        // given
        when(repository.findByUserId(USER_ID)).thenReturn(null);

        // when
        Set<Long> adverts = service.getAdverts(USER_ID);

        // then
        assertThat(adverts).isEmpty();
    }

    @Test
    public void shouldRemoveAdvertIfUserHasNoSavedAdverts() {
        // given
        when(repository.findByUserId(USER_ID)).thenReturn(null);

        // when
        service.removeAdvert(ADVERT_ID_1, USER_ID);

        // then
        verify(repository).findByUserId(USER_ID);
        verifyNoMoreInteractions(repository);
        verifyNoInteractions(migrationService);
    }

    @Test
    public void shouldRemoveAdvertIfUserHasSavedAdverts() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID)
                        .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_1 + 1))).build());

        // when
        service.removeAdvert(ADVERT_ID_1, USER_ID);

        // then
        verify(repository).save(SavedAds.builder().id("88").userId(USER_ID).advert(ADVERT_ID_1 + 1).build());
        verify(migrationService).notifyAdvertRemoved(USER_ID, ADVERT_ID_1);
    }

    @Test
    public void shouldReturnFalseHasNotSavedAdvertYet() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID).build());

        // when
        boolean actual = service.hasSavedAdvert(USER_ID, ADVERT_ID_1);

        // then
        assertThat(actual).isFalse();
    }

    @Test
    public void shouldReturnTrueHasAlreadySavedAdvert() {
        // given
        when(repository.findByUserId(USER_ID))
                .thenReturn(SavedAds.builder().id("88").userId(USER_ID)
                        .adverts(Sets.newLinkedHashSet(Lists.newArrayList(ADVERT_ID_1, ADVERT_ID_2))).build());

        // when
        boolean actual = service.hasSavedAdvert(USER_ID, ADVERT_ID_1);

        // then
        assertThat(actual).isTrue();
    }
}
