package com.gumtree.seller.service.payment.braintree;


import com.braintreegateway.Environment;
import org.hamcrest.CoreMatchers;
import org.junit.Assert;
import org.junit.Test;

public class BraintreeEnvTest {

    @Test
    public void shoulGetBraintreeEnvByStringForDevelopment() {
        Assert.assertThat(BraintreeEnv.getEnvironmentByName("DEVELOPMENT"), CoreMatchers.equalTo(Environment.DEVELOPMENT));
    }

    @Test
    public void shoulGetBraintreeEnvByStringForProduction() {
        Assert.assertThat(BraintreeEnv.getEnvironmentByName("PRODUCTION"), CoreMatchers.equalTo(Environment.PRODUCTION));
    }

    @Test
    public void shoulGetBraintreeEnvByStringForSandbox() {
        Assert.assertThat(BraintreeEnv.getEnvironmentByName("SANDBOX"), CoreMatchers.equalTo(Environment.SANDBOX));
    }
}